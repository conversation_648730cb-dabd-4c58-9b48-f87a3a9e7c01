{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": []}, "packageFolders": {"/home/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/git_file/CS/CS.csproj", "projectName": "CS", "projectPath": "/home/<USER>/git_file/CS/CS.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/git_file/CS/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/var/snap/dotnet/common/dotnet/sdk/8.0.119/PortableRuntimeIdentifierGraph.json"}}}}